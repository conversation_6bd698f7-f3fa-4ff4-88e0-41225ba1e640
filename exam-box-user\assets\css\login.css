/* ===== LOGIN SCREEN STYLES ===== */

/* ===== BOOTSTRAP OVERRIDES ===== */
:root {
    --primary-blue: #3B82F6;
    --secondary-blue: #055AA0;
    --light-gray: #F8F9FA;
    --text-gray: #6C757D;
    --border-gray: #E9ECEF;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* ===== CUSTOM COMPONENTS ===== */

/* Left Side - Illustration */
.login-screen-illustration {
    background: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%);
}

.login-screen-illustration img {
    max-width: 80%;
    height: auto;
    object-fit: contain;
}

/* Right Side - Form */
.login-screen-form-section {
    background: white;
    padding: 2rem;
}

/* Logo */
.login-screen-logo-text {
    font-size: 2rem;
    font-weight: bold;
    color: var(--primary-blue);
    letter-spacing: 1px;
}

.login-screen-logo-subtitle {
    font-size: 0.75rem;
    color: var(--text-gray);
    margin-top: 0.25rem;
    letter-spacing: 2px;
    text-transform: uppercase;
}

/* Welcome Text */
.login-screen-welcome-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #212529;
    margin-bottom: 0.5rem;
}

.login-screen-welcome-subtitle {
    color: var(--text-gray);
    font-size: 0.9rem;
}

/* Form Tabs */
.login-screen-form-tabs {
    border-radius: 8px;
    background: var(--light-gray);
    padding: 4px;
}

.login-screen-tab-button {
    padding: 0.75rem;
    border: none;
    background: transparent;
    color: var(--text-gray);
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.3s ease;
    cursor: pointer;
}

.login-screen-tab-button.active {
    background: white;
    color: #212529;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Form Fields */
.login-screen-form-label {
    color: #212529;
    font-weight: 500;
    font-size: 0.9rem;
}

.login-screen-form-input {
    padding: 0.875rem 1rem;
    border: 1px solid var(--border-gray);
    border-radius: 8px;
    font-size: 0.9rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.login-screen-form-input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.login-screen-password-toggle {
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    border: none;
    background: none;
    color: var(--text-gray);
    cursor: pointer;
    padding: 0;
}

/* Forgot Password */
.login-screen-forgot-link {
    color: var(--primary-blue);
    font-size: 0.9rem;
}

.login-screen-forgot-link:hover {
    text-decoration: underline !important;
}

/* Sign In Button */
.login-screen-signin-button {
    padding: 0.875rem;
    background: var(--secondary-blue);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

.login-screen-signin-button:hover {
    background: #044a87;
    color: white;
}

/* Create Account */
.login-screen-create-account {
    color: var(--text-gray);
    font-size: 0.9rem;
}

.login-screen-create-link {
    color: var(--primary-blue);
    font-weight: 500;
}

.login-screen-create-link:hover {
    text-decoration: underline !important;
}

/* ===== RESPONSIVE UTILITIES (Bootstrap Grid Handled) ===== */
/* Mobile adjustments using Bootstrap responsive classes in HTML */
@media (max-width: 576px) {
    .login-screen-form-section {
        padding: 1rem;
    }
    
    .login-screen-logo-text {
        font-size: 1.5rem;
    }
    
    .login-screen-welcome-title {
        font-size: 1.25rem;
    }
}

/* ===== REUSABLE COMPONENTS DOCUMENTATION ===== */
/*
REUSABLE COMPONENTS FOR OTHER PHP SCREENS:

1. .login-screen-logo and .login-screen-logo-text
   - Brand logo component with consistent styling
   - Can be reused across all authentication screens
   - Usage: Copy the logo HTML structure and these CSS classes

2. .login-screen-form-tabs and .login-screen-tab-button
   - Tab switching component for forms using Bootstrap flex utilities
   - Reusable for any screen requiring tab navigation
   - Bootstrap classes: d-flex, flex-fill for responsive behavior

3. .login-screen-form-input and .login-screen-form-group
   - Standardized form input styling with Bootstrap form-control
   - Consistent across all form screens
   - Bootstrap classes: form-control, form-label, mb-3

4. .login-screen-signin-button
   - Primary action button styling with Bootstrap btn classes
   - Can be adapted for other primary actions (Register, Submit, etc.)
   - Bootstrap classes: btn, w-100 for full width

5. Color Variables in :root
   - --primary-blue: #3B82F6 (main brand color)
   - --secondary-blue: #055AA0 (action buttons)
   - --light-gray: #F8F9FA (backgrounds)
   - --text-gray: #6C757D (secondary text)
   - --border-gray: #E9ECEF (borders and dividers)

BOOTSTRAP GRID USAGE:
- Container: container-fluid with vh-100 for full height
- Row: row with h-100 for full height
- Columns: col-lg-6 col-md-6 for 50/50 split on medium+ screens
- Responsive: Automatically stacks on mobile (col-12 default)
- Utilities: d-flex, align-items-center, justify-content-center

USAGE INSTRUCTIONS FOR PHP:
- Include this file in PHP pages using: <link rel="stylesheet" href="assets/css/login.css">
- Copy the :root variables to maintain color consistency
- Use Bootstrap grid classes for responsive layout
- Apply .login-screen-form-input class along with .form-control
- Utilize Bootstrap spacing utilities (mb-3, mt-4, etc.)
- Use Bootstrap text utilities (text-center, text-end, etc.)
*/
