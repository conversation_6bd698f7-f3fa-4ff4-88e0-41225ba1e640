/**
 * ===== LOGIN SCREEN FUNCTIONALITY =====
 * 
 * This file contains all JavaScript functionality for the login screen.
 * It includes tab switching, password toggle, form validation, and submission handling.
 * 
 * REUSABLE COMPONENTS:
 * - TabSwitcher: Handles email/phone tab switching
 * - PasswordToggle: Shows/hides password visibility
 * - FormValidator: Validates form inputs
 * - LoginHandler: Manages login form submission
 */

// ===== NAMESPACE TO PREVENT CONFLICTS =====
const LoginScreen = {
    
    // ===== INITIALIZATION =====
    init: function() {
        this.initTabSwitching();
        this.initPasswordToggle();
        this.initFormSubmission();
    },

    // ===== TAB SWITCHING FUNCTIONALITY =====
    initTabSwitching: function() {
        const tabButtons = document.querySelectorAll('.login-screen-tab-button');
        const emailInput = document.getElementById('emailInput');
        const emailLabel = document.getElementById('emailLabel');
        
        if (!tabButtons.length || !emailInput || !emailLabel) {
            console.warn('Login Screen: Tab switching elements not found');
            return;
        }

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all tabs
                tabButtons.forEach(tab => tab.classList.remove('active'));
                // Add active class to clicked tab
                this.classList.add('active');
                
                // Update form based on selected tab
                const tabType = this.getAttribute('data-tab');
                LoginScreen.updateFormForTab(tabType, emailInput, emailLabel);
            });
        });
    },

    // ===== UPDATE FORM BASED ON TAB SELECTION =====
    updateFormForTab: function(tabType, emailInput, emailLabel) {
        if (tabType === 'email') {
            emailInput.type = 'email';
            emailInput.placeholder = 'Enter your email';
            emailLabel.textContent = 'Email address';
            emailInput.setAttribute('pattern', '');
        } else if (tabType === 'phone') {
            emailInput.type = 'tel';
            emailInput.placeholder = 'Enter your phone number';
            emailLabel.textContent = 'Phone number';
            emailInput.setAttribute('pattern', '[0-9+\\-\\s\\(\\)]+');
        }
        
        // Clear any existing validation messages
        emailInput.setCustomValidity('');
    },

    // ===== PASSWORD TOGGLE FUNCTIONALITY =====
    initPasswordToggle: function() {
        const passwordToggle = document.getElementById('passwordToggle');
        const passwordInput = document.getElementById('passwordInput');
        
        if (!passwordToggle || !passwordInput) {
            console.warn('Login Screen: Password toggle elements not found');
            return;
        }

        passwordToggle.addEventListener('click', function() {
            const isPassword = passwordInput.getAttribute('type') === 'password';
            const newType = isPassword ? 'text' : 'password';
            
            passwordInput.setAttribute('type', newType);
            
            // Toggle eye icon
            const icon = this.querySelector('i');
            if (icon) {
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            }
            
            // Update aria-label for accessibility
            this.setAttribute('aria-label', isPassword ? 'Hide password' : 'Show password');
        });
    },

    // ===== FORM VALIDATION =====
    validateForm: function(emailOrPhone, password, tabType) {
        const errors = [];

        // Validate email or phone based on active tab
        if (!emailOrPhone.trim()) {
            errors.push(`Please enter your ${tabType === 'email' ? 'email address' : 'phone number'}`);
        } else if (tabType === 'email') {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(emailOrPhone)) {
                errors.push('Please enter a valid email address');
            }
        } else if (tabType === 'phone') {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(emailOrPhone.replace(/\s/g, ''))) {
                errors.push('Please enter a valid phone number');
            }
        }

        // Validate password
        if (!password.trim()) {
            errors.push('Please enter your password');
        } else if (password.length < 6) {
            errors.push('Password must be at least 6 characters long');
        }

        return errors;
    },

    // ===== FORM SUBMISSION HANDLING =====
    initFormSubmission: function() {
        const loginForm = document.getElementById('loginForm');
        
        if (!loginForm) {
            console.warn('Login Screen: Login form not found');
            return;
        }

        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            // Get form values
            const emailInput = document.getElementById('emailInput');
            const passwordInput = document.getElementById('passwordInput');
            const activeTab = document.querySelector('.login-screen-tab-button.active');
            
            if (!emailInput || !passwordInput || !activeTab) {
                console.error('Login Screen: Required form elements not found');
                return;
            }

            const emailOrPhone = emailInput.value;
            const password = passwordInput.value;
            const tabType = activeTab.getAttribute('data-tab');

            // Validate form
            const validationErrors = LoginScreen.validateForm(emailOrPhone, password, tabType);
            
            if (validationErrors.length > 0) {
                LoginScreen.showValidationErrors(validationErrors);
                return;
            }

            // Process login
            LoginScreen.processLogin({
                emailOrPhone: emailOrPhone,
                password: password,
                type: tabType
            });
        });
    },

    // ===== SHOW VALIDATION ERRORS =====
    showValidationErrors: function(errors) {
        // Remove existing error messages
        const existingErrors = document.querySelectorAll('.login-screen-error-message');
        existingErrors.forEach(error => error.remove());

        // Create and show new error message
        const errorContainer = document.createElement('div');
        errorContainer.className = 'login-screen-error-message alert alert-danger mt-3';
        errorContainer.innerHTML = `
            <i class="fas fa-exclamation-triangle me-2"></i>
            ${errors.join('<br>')}
        `;

        // Insert error message before the submit button
        const submitButton = document.querySelector('.login-screen-signin-button');
        if (submitButton) {
            submitButton.parentNode.insertBefore(errorContainer, submitButton);
        }

        // Auto-remove error after 5 seconds
        setTimeout(() => {
            if (errorContainer.parentNode) {
                errorContainer.remove();
            }
        }, 5000);
    },

    // ===== PROCESS LOGIN =====
    processLogin: function(loginData) {
        // Show loading state
        const submitButton = document.querySelector('.login-screen-signin-button');
        const originalText = submitButton.textContent;
        
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Signing In...';

        // Simulate API call (replace with actual login logic)
        console.log('Login attempt:', loginData);
        
        // Here you would typically send the data to your backend
        // Example PHP AJAX call:
        /*
        fetch('login_process.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(loginData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = 'dashboard.php';
            } else {
                LoginScreen.showValidationErrors([data.message || 'Login failed']);
            }
        })
        .catch(error => {
            LoginScreen.showValidationErrors(['Network error. Please try again.']);
        })
        .finally(() => {
            submitButton.disabled = false;
            submitButton.textContent = originalText;
        });
        */

        // For demo purposes, show success message after 2 seconds
        setTimeout(() => {
            submitButton.disabled = false;
            submitButton.textContent = originalText;
            
            // Show success message
            const successContainer = document.createElement('div');
            successContainer.className = 'alert alert-success mt-3';
            successContainer.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                Login successful! Redirecting...
            `;
            
            const form = document.getElementById('loginForm');
            form.appendChild(successContainer);
            
            // Simulate redirect after 1 second
            setTimeout(() => {
                alert('Login functionality would redirect to dashboard.php here');
            }, 1000);
        }, 2000);
    }
};

// ===== INITIALIZE ON DOM CONTENT LOADED =====
document.addEventListener('DOMContentLoaded', function() {
    LoginScreen.init();
});

// ===== REUSABLE COMPONENTS DOCUMENTATION =====
/*
REUSABLE JAVASCRIPT COMPONENTS FOR PHP APPLICATIONS:

1. LoginScreen.initTabSwitching()
   - Handles tab switching functionality
   - Can be adapted for other screens with tabs
   - Usage: Call this method for any tab-based interface

2. LoginScreen.initPasswordToggle()
   - Password visibility toggle functionality
   - Reusable across all password input fields
   - Requires: .login-screen-password-toggle and password input

3. LoginScreen.validateForm()
   - Form validation with email/phone support
   - Returns array of validation errors
   - Can be customized for different validation rules

4. LoginScreen.showValidationErrors()
   - Displays validation errors with Bootstrap styling
   - Auto-removes errors after 5 seconds
   - Consistent error display across all forms

5. LoginScreen.processLogin()
   - Handles form submission with loading states
   - Template for PHP backend integration
   - Shows success/error feedback

INTEGRATION INSTRUCTIONS FOR PHP:
- Include this file in your PHP pages using: <script src="assets/js/login.js"></script>
- Modify processLogin() method for your PHP backend API
- Create login_process.php for handling form submissions
- Use consistent class names for styling compatibility
- Ensure Bootstrap and FontAwesome are loaded for proper functionality
*/
