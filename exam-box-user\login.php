<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ExamBox - Welcome Back</title>
    
    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- FontAwesome Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/login.php">
</head>
<body>
    <!-- Login Screen Container -->
    <div class="container-fluid login-screen-container vh-100">
        <div class="row h-100">
            <!-- Left Side - Illustration -->
            <div class="col-lg-6 col-md-6 d-flex align-items-center justify-content-center login-screen-illustration p-0">
                <img src="assets/images/desktop-img.jpg" alt="ExamBox Learning Platform" class="img-fluid">
            </div>

            <!-- Right Side - Form Section -->
            <div class="col-lg-6 col-md-6 d-flex align-items-center justify-content-center login-screen-form-section">
                <div class="login-screen-form-container w-100" style="max-width: 400px;">
                    <!-- Logo -->
                    <div class="text-center mb-4 login-screen-logo">
                        <a href="#" class="login-screen-logo-text text-decoration-none">EXAMBOX</a>
                        <div class="login-screen-logo-subtitle">Learning Platform Solution</div>
                    </div>

                    <!-- Welcome Text -->
                    <div class="text-center mb-4 login-screen-welcome">
                        <h1 class="login-screen-welcome-title">Welcome back</h1>
                        <p class="login-screen-welcome-subtitle">Please enter your details to sign in</p>
                    </div>

                    <!-- Form Tabs -->
                    <div class="d-flex mb-4 login-screen-form-tabs">
                        <button class="flex-fill login-screen-tab-button active" data-tab="email">Email</button>
                        <button class="flex-fill login-screen-tab-button" data-tab="phone">Phone</button>
                    </div>

                    <!-- Login Form -->
                    <form id="loginForm" class="login-screen-form">
                        <!-- Email/Phone Field -->
                        <div class="mb-3 login-screen-form-group">
                            <label for="emailInput" class="form-label login-screen-form-label" id="emailLabel">Email address</label>
                            <input type="email" class="form-control login-screen-form-input" id="emailInput" placeholder="Enter your email" required>
                        </div>

                        <!-- Password Field -->
                        <div class="mb-3 login-screen-form-group">
                            <label for="passwordInput" class="form-label login-screen-form-label">Password</label>
                            <div class="position-relative login-screen-password-container">
                                <input type="password" class="form-control login-screen-form-input" id="passwordInput" placeholder="Enter your password" required>
                                <button type="button" class="btn position-absolute login-screen-password-toggle" id="passwordToggle">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Forgot Password -->
                        <div class="text-end mb-4 login-screen-forgot-password">
                            <a href="#" class="login-screen-forgot-link text-decoration-none">Forgot password?</a>
                        </div>

                        <!-- Sign In Button -->
                        <button type="submit" class="btn w-100 login-screen-signin-button">Sign In</button>

                        <!-- Create Account -->
                        <div class="text-center mt-4 login-screen-create-account">
                            Don't have an account? <a href="#" class="login-screen-create-link text-decoration-none">Create Account</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="assets/js/login.php"></script>
</body>
</html>
